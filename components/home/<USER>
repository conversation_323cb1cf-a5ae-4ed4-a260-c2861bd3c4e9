import Image from 'next/image';
import Link from 'next/link';
import {
  HardDrive,
  Users,
  Shield,
  Zap,
  Container,
  Bot,
  Brain,
  FileText,
  Image as ImageIcon,
  Cloud,
  Globe,
  ExternalLink,
  CheckCircle,
  Star,
  ArrowRight,
  Sparkles,
  IdCard,
  Menu
} from 'lucide-react';
import { AIProviderLogo } from './shared/AIProviderLogo';
import type { HomePageContent } from './shared/types';
import LanguageSwitcher from '@/components/LanguageSwitcher';

// 中文版内容配置
export const cnContent: HomePageContent = {
  metadata: {
    title: 'HiveChat - 开源AI聊天平台 | 私有部署 多用户管理 MCP支持',
    description: '为中小团队设计的开源AI聊天应用，支持私有部署、多用户管理、权限控制、MCP协议，一站式接入OpenAI、Claude、Gemini等主流AI服务商，安全可靠的企业级AI解决方案',
    keywords: [
      'HiveChat',
      '开源AI聊天',
      'AI聊天平台',
      '私有部署',
      '多用户管理',
      'MCP协议',
      'OpenAI',
      'Claude',
      'Gemini',
      '企业AI',
      'AI助手',
      '人工智能',
      '聊天机器人'
    ],
    authors: [{ name: 'HiveNexus Team' }],
    creator: 'HiveNexus',
    publisher: 'HiveNexus',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    category: 'Technology',
    classification: 'AI Chat Platform',
    other: {
      'application-name': 'HiveChat',
      'apple-mobile-web-app-title': 'HiveChat',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'format-detection': 'telephone=no',
      'mobile-web-app-capable': 'yes',
      'msapplication-TileColor': '#2563eb',
      'msapplication-tap-highlight': 'no',
      'theme-color': '#ffffff',
    },
  },
  navigation: [
    { name: '功能特性', href: '#features' },
    { name: '部署方案', href: '#deployment' },
    { name: '在线演示', href: 'https://chat.yotuku.cn/', external: true },
    { name: '查看文档', href: '/docs' },
    { name: 'GitHub', href: 'https://github.com/HiveNexus/HiveChat', external: true },
  ],
  hero: {
    subtitle: '为中小团队设计的 AI 聊天应用，支持多用户模式、权限管理、MCP 协议，以及所有主流 AI 服务商接入。',
    ctaButtons: {
      demo: '立即体验',
      docs: '查看文档'
    },
    tags: ['开源免费', '私有部署', '多用户管理', 'MCP 支持', '所有主流服务商支持']
  },
  sections: {
    aiModels: {
      title: '支持所有主流 AI 服务商',
      subtitle: '一站式接入全球顶级 AI 服务商，满足不同场景需求'
    },
    coreFeatures: {
      title: '核心功能特性',
      features: [
        {
          icon: Users,
          title: '多用户模式',
          desc: '支持团队协作，用户分组管理，权限精细控制'
        },
        {
          icon: Shield,
          title: '权限管理',
          desc: '灵活的用户权限体系，Token 限额控制，安全可靠'
        },
        {
          icon: Zap,
          title: 'MCP 协议支持',
          desc: '支持 Model Context Protocol，扩展 AI 能力边界'
        },
      ]
    },
    featuresGrid: {
      title: '丰富的功能特性',
      features: [
        { icon: Brain, title: '思维链', desc: '可视化展示 AI 思考过程' },
        { icon: FileText, title: 'LaTeX & Markdown', desc: '完美渲染数学公式和文档' },
        { icon: ImageIcon, title: '图像理解', desc: '支持图片上传和多模态对话' },
        { icon: Bot, title: 'AI 智能体', desc: '自定义 AI 助手和工作流' },
        { icon: Cloud, title: '云端存储', desc: '安全可靠的数据存储方案' },
        { icon: IdCard, title: '企业登录集成', desc: '支持邮箱、企业微信、钉钉、飞书登录' },
      ]
    },
    deployment: {
      title: '灵活的部署方式',
      subtitle: '支持多种部署方案，满足不同团队需求',
      options: [
        {
          title: '本地部署',
          description: '完全私有化部署，数据安全可控',
          features: ['数据完全私有', '自定义配置', '无外网依赖'],
          icon: HardDrive,
        },
        {
          title: 'Docker 部署',
          description: '一键容器化部署，简单快捷',
          features: ['快速启动', '环境隔离', '易于维护'],
          icon: Container,
        },
        {
          title: 'Vercel 部署',
          description: '云端 SaaS 服务，开箱即用',
          features: ['零运维', '自动扩容', '全球加速'],
          icon: Globe,
        },
      ]
    },
    demo: {
      title: '立即开始体验',
      subtitle: '在线演示或下载源码，快速上手 HiveChat',
      onlineDemo: {
        title: '在线演示',
        description: '体验完整功能，无需安装',
        userDemo: {
          title: '用户端演示',
          url: 'https://chat.yotuku.cn/',
          note: '可自行注册账号体验'
        },
        adminDemo: {
          title: '管理员端演示',
          url: 'https://hivechat-demo.vercel.app/',
          credentials: 'Email: <EMAIL> / Password: helloHivechat'
        }
      },
      github: {
        title: '开源仓库',
        description: '获取源码，自由定制和部署',
        button: '查看源码',
        badges: {
          openSource: '开源免费',
          license: 'Apache 2.0 协议'
        }
      }
    },
    cta: {
      title: '准备好开始了吗？',
      subtitle: '立即体验 HiveChat，为您的团队打造专属的 AI 聊天平台',
      buttons: {
        demo: '立即体验',
        docs: '查看文档'
      }
    },
    footer: {
      copyright: '© 2025 HiveChat',
      links: {
        privacy: '隐私政策',
        terms: '服务条款'
      }
    }
  },
  structuredData: {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "HiveChat",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web",
    "description": "为中小团队设计的开源AI聊天应用，支持私有部署、多用户管理、权限控制、MCP协议，一站式接入15+主流AI服务商",
    "url": "https://www.hivechat.net",
    "downloadUrl": "https://github.com/HiveNexus/HiveChat",
    "author": {
      "@type": "Organization",
      "name": "HiveNexus",
      "url": "https://github.com/HiveNexus"
    },
    "publisher": {
      "@type": "Organization",
      "name": "HiveNexus"
    },
    "license": "https://www.apache.org/licenses/LICENSE-2.0",
    "programmingLanguage": "TypeScript",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "开源免费",
      "私有部署",
      "多用户管理",
      "MCP协议支持",
      "15+AI服务商支持",
      "权限管理",
      "企业级安全"
    ],
    "screenshot": "https://hivechat.yotuku.cn/images/og-image.png",
    "softwareVersion": "1.0.0",
    "datePublished": "2024-01-01",
    "dateModified": "2024-07-16"
  }
};

// Header 组件 - 使用纯 CSS 实现移动端菜单
function Header({ content }: { content: HomePageContent }) {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/85 backdrop-blur-xl border-b border-white/20 shadow-lg shadow-black/5">
      <nav className="mx-auto max-w-7xl px-6 lg:px-8" aria-label="Top">
        {/* Mobile menu toggle checkbox - 必须在 nav 内部才能使用 peer 选择器 */}
        <input type="checkbox" id="mobile-menu-toggle" className="hidden peer" />

        <div className="flex w-full items-center justify-between py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3">
              <Image src="/images/icon.png" alt="HiveChatLogo" width={32} height={32} className="rounded-lg" />
              <Image src="/images/hivechat.svg" alt="HiveChat" width={110} height={28} />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-8">
            {content.navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                target={item.external ? '_blank' : undefined}
                className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200"
              >
                {item.name}
                {item.external && <ExternalLink className="ml-1 h-3 w-3 inline" />}
              </Link>
            ))}
            {/* Language Switcher */}
            <LanguageSwitcher currentLang="cn" />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <label
              htmlFor="mobile-menu-toggle"
              className="rounded-md p-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200 cursor-pointer inline-flex items-center justify-center"
            >
              <span className="sr-only">打开菜单</span>
              <Menu className="h-6 w-6" aria-hidden="true" />
            </label>
          </div>
        </div>

        {/* Mobile Navigation - 现代化样式，纯白背景，清晰分割线，支持滚动 */}
        <div className="mobile-menu-container md:hidden overflow-hidden transition-all duration-300 ease-in-out max-h-0 peer-checked:max-h-[32rem] bg-white border-t border-gray-100 shadow-lg absolute left-0 right-0 z-50">
          <div className="mobile-menu-content max-h-[30rem] overflow-y-auto divide-y divide-gray-50">
            {content.navigation.map((item) => (
              <div key={item.name} className="relative">
                {/* 菜单项容器 - 使用 label 包装实现点击关闭功能 */}
                <label htmlFor="mobile-menu-toggle" className="block cursor-pointer">
                  <Link
                    href={item.href}
                    target={item.external ? '_blank' : undefined}
                    className="flex items-center justify-between px-6 py-4 text-base font-medium text-gray-800 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 group active:bg-blue-100"
                  >
                    <span className="flex items-center">
                      {item.name}
                      {item.external && <ExternalLink className="ml-2 h-4 w-4 opacity-60 group-hover:opacity-100" />}
                    </span>
                    {/* 添加右箭头指示器，提升现代感 */}
                    <ArrowRight className="h-4 w-4 opacity-30 group-hover:opacity-60 group-hover:translate-x-1 transition-all duration-200" />
                  </Link>
                </label>
              </div>
            ))}
            {/* Mobile Language Switcher */}
            <LanguageSwitcher currentLang="cn" isMobile={true} />
          </div>
        </div>

        {/* 移动端菜单背景遮罩 - 更现代的深色遮罩 */}
        <label
          htmlFor="mobile-menu-toggle"
          className="md:hidden fixed inset-0 bg-black/40 z-40 opacity-0 peer-checked:opacity-100 transition-opacity duration-300 pointer-events-none peer-checked:pointer-events-auto cursor-pointer"
          aria-label="关闭菜单"
        />
      </nav>
    </header>
  );
}

export default function HomeCN() {
  const content = cnContent;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(content.structuredData) }}
      />

      {/* Header */}
      <Header content={content} />

      {/* Hero Section */}
      <section className="relative px-6 lg:px-8 pt-32 pb-32">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <div className='flex flex-row items-center justify-center'>
              <Image src="/images/icon.png" alt="HiveChat" width={60} height={60} className="rounded-2xl" />
              <Image src="/images/hivechat.svg" alt="HiveChat" className='ml-4' width={190} height={28} />
            </div>
            <p className="mt-6 text-xl leading-8 text-gray-600 max-w-3xl mx-auto">
              {content.hero.subtitle}
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="https://chat.yotuku.cn/"
                target="_blank"
                className="rounded-md bg-blue-600 px-6 py-2 text-lg font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors"
              >
                {content.hero.ctaButtons.demo}
              </Link>
              <Link
                href="/docs"
                className="text-lg font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors"
              >
                {content.hero.ctaButtons.docs} <ArrowRight className="ml-2 h-5 w-5 inline" />
              </Link>
            </div>

            {/* 特性标签 */}
            <div className="mt-12 flex flex-wrap justify-center gap-3">
              {content.hero.tags.map((tag) => (
                <span key={tag} className="inline-flex items-center rounded-full bg-blue-100 px-4 py-2 text-sm font-medium text-blue-800">
                  <Sparkles className="mr-1 h-4 w-4" />
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* AI Models Section */}
      <section className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {content.sections.aiModels.title}
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              {content.sections.aiModels.subtitle}
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-5xl grid-cols-2 gap-6 sm:grid-cols-3 lg:grid-cols-5">
            {[
              'OpenAI', 'Claude', 'Gemini', 'DeepSeek', 'Qwen',
              'Moonshot', 'Minimax', 'Doubao', 'Hunyuan', 'Baidu',
              'SiliconFlow', 'Grok', 'Ollama', 'OpenRouter', 'Volcengine'
            ].map((model) => (
              <AIProviderLogo key={model} name={model} />
            ))}
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section id="features" className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {content.sections.coreFeatures.title}
            </h2>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-8 lg:max-w-none lg:grid-cols-3">
              {content.sections.coreFeatures.features.map((feature) => (
                <div key={feature.title} className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <feature.icon className="h-5 w-5 flex-none text-blue-600" aria-hidden="true" />
                    {feature.title}
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">{feature.desc}</p>
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {content.sections.featuresGrid.title}
            </h2>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-8 lg:max-w-none lg:grid-cols-3">
              {content.sections.featuresGrid.features.map((feature) => (
                <div key={feature.title} className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <feature.icon className="h-5 w-5 flex-none text-blue-600" aria-hidden="true" />
                    {feature.title}
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">{feature.desc}</p>
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </section>

      {/* Deployment Options */}
      <section id="deployment" className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {content.sections.deployment.title}
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              {content.sections.deployment.subtitle}
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {content.sections.deployment.options.map((option) => (
                <div key={option.title} className="rounded-2xl bg-white p-8 shadow-sm ring-1 ring-gray-200">
                  <option.icon className="h-8 w-8 text-blue-600" />
                  <h3 className="mt-4 text-lg font-semibold text-gray-900">{option.title}</h3>
                  <p className="mt-2 text-sm text-gray-600">{option.description}</p>
                  <ul className="mt-4 space-y-2">
                    {option.features.map((feature) => (
                      <li key={feature} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Demo & GitHub */}
      <section id="demo" className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {content.sections.demo.title}
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              {content.sections.demo.subtitle}
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-8 lg:grid-cols-2">
            {/* 在线演示 */}
            <div className="rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
              <div className="flex items-center">
                <h3 className="text-xl font-semibold text-gray-900">{content.sections.demo.onlineDemo.title}</h3>
              </div>
              <p className="mt-4 text-gray-600">
                {content.sections.demo.onlineDemo.description}
              </p>
              <div className="mt-6 space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-900">{content.sections.demo.onlineDemo.userDemo.title}</p>
                  <Link
                    href={content.sections.demo.onlineDemo.userDemo.url}
                    target="_blank"
                    className="text-sm text-blue-600 hover:text-blue-500"
                  >
                    {content.sections.demo.onlineDemo.userDemo.url}
                  </Link>
                  <p className="text-xs text-gray-500">{content.sections.demo.onlineDemo.userDemo.note}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">{content.sections.demo.onlineDemo.adminDemo.title}</p>
                  <Link
                    href={content.sections.demo.onlineDemo.adminDemo.url}
                    target="_blank"
                    className="text-sm text-blue-600 hover:text-blue-500"
                  >
                    {content.sections.demo.onlineDemo.adminDemo.url}
                  </Link>
                  <p className="text-xs text-gray-500">{content.sections.demo.onlineDemo.adminDemo.credentials}</p>
                </div>
              </div>
            </div>

            {/* GitHub 仓库 */}
            <div className="rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100 p-8">
              <div className="flex items-center">
                <h3 className="text-xl font-semibold text-gray-900">{content.sections.demo.github.title}</h3>
              </div>
              <p className="mt-4 text-gray-600">
                {content.sections.demo.github.description}
              </p>
              <div className="mt-6">
                <Link
                  href="https://github.com/HiveNexus/HiveChat"
                  target="_blank"
                  className="inline-flex items-center rounded-md bg-gray-900 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-800 transition-colors"
                >
                  <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                  {content.sections.demo.github.button}
                </Link>
                <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <Star className="mr-1 h-4 w-4" />
                    {content.sections.demo.github.badges.openSource}
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="mr-1 h-4 w-4" />
                    {content.sections.demo.github.badges.license}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-700">
        <div className="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              {content.sections.cta.title}
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-blue-100">
              {content.sections.cta.subtitle}
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="https://chat.yotuku.cn/"
                target="_blank"
                className="rounded-md bg-white px-6 py-2 text-lg font-semibold text-blue-600 shadow-sm hover:bg-blue-50 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors"
              >
                {content.sections.cta.buttons.demo}
              </Link>
              <Link
                href="/docs"
                className="text-lg font-semibold leading-6 text-white hover:text-blue-100 transition-colors"
              >
                {content.sections.cta.buttons.docs} <ArrowRight className="ml-2 h-5 w-5 inline" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      <footer className="bg-gray-50 border-t border-gray-200">
        <div className="mx-auto max-w-7xl px-6 py-8 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-sm text-gray-600">
              <span>{content.sections.footer.copyright}</span>
              <span className="text-gray-200 mx-2">|</span>
              <Link
                href="https://cloud.hivechat.net/privacy"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-600 transition-colors duration-200"
              >
                {content.sections.footer.links.privacy}
              </Link>
              <span className="text-gray-200 mx-2">|</span>
              <Link
                href="https://cloud.hivechat.net/terms"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-600 transition-colors duration-200"
              >
                {content.sections.footer.links.terms}
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
