import Link from 'next/link';

interface LanguageSwitcherProps {
  currentLang: 'cn' | 'en';
  className?: string;
  isMobile?: boolean;
}

export default function LanguageSwitcher({ currentLang, className = '', isMobile = false }: LanguageSwitcherProps) {
  // 对于首页，直接使用语言路径
  const languages = [
    {
      code: 'cn',
      name: '中文',
      shortName: 'CN',
      href: '/cn',
    },
    {
      code: 'en',
      name: 'English',
      shortName: 'EN',
      href: '/en',
    },
  ];

  if (isMobile) {
    return (
      <div className={`divide-y divide-gray-50 ${className}`}>
        {languages.map((lang) => (
          <div key={lang.code} className="relative">
            <label htmlFor="mobile-menu-toggle" className="block cursor-pointer">
              <Link
                href={lang.href}
                className={`flex items-center justify-between px-6 py-4 text-base font-medium transition-all duration-200 group ${
                  currentLang === lang.code
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-800 hover:bg-blue-50 hover:text-blue-600'
                }`}
              >
                <span className="flex items-center">
                  {lang.name}
                  {currentLang === lang.code && (
                    <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">
                      {currentLang === 'cn' ? '当前' : 'Current'}
                    </span>
                  )}
                </span>
              </Link>
            </label>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {languages.map((lang, index) => (
        <div key={lang.code} className="flex items-center">
          <Link
            href={lang.href}
            className={`px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
              currentLang === lang.code
                ? 'text-blue-600 bg-blue-50 border border-blue-200'
                : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
            }`}
            title={`切换到${lang.name}`}
          >
            {lang.shortName}
          </Link>
          {index < languages.length - 1 && (
            <span className="mx-1 text-gray-300">|</span>
          )}
        </div>
      ))}
    </div>
  );
}
