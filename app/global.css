@import 'tailwindcss';
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 移动端菜单动画效果 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 菜单按钮旋转效果 - 只选择菜单按钮，不影响菜单项内的图标 */
#mobile-menu-toggle:checked ~ div .md\:hidden label svg {
  transform: rotate(90deg);
}

/* 只为菜单按钮添加过渡效果 */
.md\:hidden label svg {
  transition: transform 0.3s ease;
}

/* 移动端菜单显示时的动画 */
#mobile-menu-toggle:checked ~ div .peer-checked\:max-h-\[32rem\] {
  animation: slideDown 0.3s ease-out;
}

/* 确保移动端菜单在所有设备上都能正确显示 */
@media (max-width: 768px) {
  .mobile-menu-container {
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    z-index: 50;
    background: white;
    border-top: 1px solid #f3f4f6;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .mobile-menu-content {
    max-height: 80vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* AI 服务商 Logo 容器样式 */
.ai-provider-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

/* AI 服务商 Logo 图片优化 */
.ai-provider-logo-container img {
  transition: opacity 0.2s ease-in-out;
}

.ai-provider-logo-container img:hover {
  opacity: 0.8;
}
